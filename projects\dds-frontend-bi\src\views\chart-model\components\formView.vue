<template>
  <el-dialog
    :title="dialogTitle"
    v-if="dialogVisible"
    :visible.sync="dialogVisible"
    width="880px"
    :before-close="handleClose"
    :close-on-click-modal="false"
  >
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <!-- <el-form-item label="图表ID">
        <el-input v-model="form.id"></el-input>
      </el-form-item> -->
      <el-form-item label="图表模型名称" prop="chartName">
        <el-input v-model="form.chartName"></el-input>
      </el-form-item>
      <el-form-item label="选择指标" style="margin-bottom: 0" prop="chartInds">
        <div
          v-for="(item, index) in form.chartInds"
          :key="index"
          style="margin-bottom: 18px; display: flex"
        >
          <el-form-item
            :prop="'chartInds.' + index + '.indCode'"
            :rules="{
              required: true,
              message: '请选择指标',
              trigger: 'blur'
            }"
            style="margin-bottom: 0"
          >
            <el-select-v2
              v-model="item.indCode"
              :options="allIndList"
              style="width: 220px"
              :props="{
                label: 'zbmc',
                value: 'indCode'
              }"
              filterable
              placeholder="请选择"
              @change="val => getDim(val, index, true)"
              clearable
            >
              <template #default="{ item }">
                <p
                  style="
                    padding: 0 17px;
                    display: flex;
                    justify-content: space-between;
                  "
                >
                  <span v-tooltip-content="180">{{ item.zbmc }}</span>
                  <span style="color: #8492a6; font-size: 13px">
                    {{ item.zblx }}
                  </span>
                </p>
              </template>
            </el-select-v2>
          </el-form-item>

          <el-checkbox
            v-model="item.isOrNotDrillDown"
            :true-label="1"
            :false-label="0"
            style="margin-left: 10px"
          >
            开启聚类
          </el-checkbox>

          <el-select
            v-if="item.isOrNotDrillDown === 1"
            v-model="item.chartDims"
            clearable
            placeholder="请选择维度"
            style="width: 200px; margin-left: 10px"
            class="myselect"
            value-key="levelCode"
            @change="selectIdxDim($event, index)"
          >
            <el-option
              v-for="ele in optionsMap[item.indCode]"
              :key="ele.levelCode"
              :label="ele.levelName"
              :value="ele"
            ></el-option>
          </el-select>

          <LevelMultipleSelect
            v-model="item.chartDims.dimValList"
            v-if="
              item.chartDims.levelCode &&
              !item.chartDims.enableClustering &&
              item.isOrNotDrillDown === 1 &&
              item.chartDims.fieldType !== 'time'
            "
            :is-selected-all="item.chartDims.isSelectedAll"
            :is-selected-all-name="item.chartDims.isSelectedAllName"
            style="width: 205px; margin-left: 6px"
            :level-code="item.chartDims.levelCode"
          />
          <ClusterMultipleSelect
            v-if="
              item.chartDims.levelCode &&
              item.chartDims.enableClustering &&
              item.isOrNotDrillDown === 1 &&
              item.chartDims.fieldType !== 'time'
            "
            :dim-values.sync="item.chartDims.dimValList"
            v-model="item.chartDims.clusterCodes"
            :is-selected-all="item.chartDims.isSelectedAll"
            :is-selected-all-name="item.chartDims.isSelectedAllName"
            :level-code="item.chartDims.levelCode"
            style="width: 205px; margin-left: 6px"
          />
          <el-select
            v-if="
              item.isOrNotDrillDown === 1 && item.chartDims.fieldType === 'time'
            "
            v-model="item.chartDims.dimValList"
            clearable
            placeholder="请选择维度"
            style="width: 200px; margin-left: 10px"
          >
            <el-option
              v-for="ele in timeDimension"
              :key="ele.label"
              :label="ele.label"
              :value="ele.value"
            ></el-option>
          </el-select>
          <el-button
            style="margin-left: 10px; height: 33px"
            type="danger"
            icon="el-icon-minus"
            v-if="form.chartInds.length > 1"
            circle
            @click="removeIndicators(index)"
          ></el-button>
          <el-button
            style="margin-left: 10px; height: 33px"
            type="primary"
            icon="el-icon-plus"
            circle
            @click="addIndicators"
          ></el-button>
        </div>
      </el-form-item>

      <el-form-item label="可过滤维度" style="margin-bottom: 0">
        <div
          class="form-item"
          v-for="(item, index) in form.chartDims"
          :key="index"
        >
          <div style="display: flex; margin-bottom: 18px">
            <el-select
              v-model="item.dimCol"
              clearable
              placeholder="请选择过滤维度"
              @change="changeDim($event, 0)"
              style="width: 200px"
              class="myselect"
            >
              <el-option
                v-for="ele in sameDimension"
                :key="ele.dimCol"
                :label="ele.levelName"
                :value="ele.dimCol"
              ></el-option>
            </el-select>
            <LevelMultipleSelect
              v-model="item.dimValList"
              v-if="item.levelCode && !item.enableClustering"
              :is-selected-all="item.isSelectedAll"
              :is-selected-all-name="item.isSelectedAllName"
              style="width: 205px; margin-left: 6px"
              :level-code="item.levelCode"
            />
            <ClusterMultipleSelect
              v-if="item.levelCode && item.enableClustering"
              :dim-values.sync="item.dimValList"
              v-model="item.clusterCodes"
              :is-selected-all="item.isSelectedAll"
              :is-selected-all-name="item.isSelectedAllName"
              :level-code="item.levelCode"
              style="width: 205px; margin-left: 6px"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item label="时间范围">
        <el-select v-model="form.dateTime" placeholder="请选择时间范围">
          <el-option
            v-for="(item, index) in timeRange"
            :key="index"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="图表类型" prop="chartType">
        <el-select v-model="form.chartType" placeholder="请选择图表类型" style="width: 200px">
          <el-option
            v-for="item in chartTypeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
        <el-button
          type="primary"
          @click="handlePreview"
          :disabled="!form.chartType || !canPreview"
          style="margin-left: 10px"
        >
          预览
        </el-button>
      </el-form-item>
      <el-form-item label="描述信息">
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请输入指标描述信息"
          v-model="form.description"
        ></el-input>
      </el-form-item>
    </el-form>

    <!-- 图表预览区域 -->
    <div v-if="showPreview" class="chart-preview-container">
      <div class="preview-header">
        <h4>图表预览</h4>
        <el-button type="text" @click="showPreview = false" icon="el-icon-close"></el-button>
      </div>
      <div class="preview-content" v-loading="previewLoading">
        <div v-if="previewError" class="preview-error">
          {{ previewError }}
        </div>
        <div v-else-if="currentChartComponent" class="chart-container">
          <!-- 动态图表组件 -->
          <component
            :is="currentChartComponent"
            v-bind="chartProps"
            @chartClick="handleChartClick"
          />
        </div>
        <div v-else class="preview-empty">
          请选择图表类型进行预览
        </div>
      </div>
    </div>

    <span slot="footer" class="dialog-footer">
      <el-button @click="handleClose">取 消</el-button>
      <el-button type="primary" @click="handleSave" :loading="loading">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import Request from '@/service'
import LevelMultipleSelect from '../../indicator-anagement/components/LevelMultipleSelect.vue'
import ClusterMultipleSelect from '../../indicator-anagement/components/ClusterMultipleSelect.vue'
import { chartComponentMap, getStaticPreviewData, getChartProps } from './chartComponents.js'
export default {
  components: {
    LevelMultipleSelect,
    ClusterMultipleSelect
  },
  props: {},
  data () {
    return {
      dialogTitle: '新建图表',
      dialogVisible: false,
      form: {
        chartName: '',
        dateTime: '',
        chartType: '', // 新增图表类型字段
        chartInds: [
          {
            indCode: '',
            isOrNotDrillDown: 0,
            lxbm: '',
            chartDims: {}
          }
        ],
        chartDims: [
          {
            dimIndCode: '',
            dimCol: '',
            dimName: '',
            dimValList: [],
            dimType: 'filter'
          }
        ],
        description: ''
      },
      rules: {
        chartName: [
          { required: true, message: '请输入图表名称', trigger: 'blur' }
        ],
        chartInds: [{ required: true, message: '请选择指标', trigger: 'blur' }],
        chartType: [
          { required: true, message: '请选择图表类型', trigger: 'change' }
        ]
      },
      optionsMap: {},
      sameDimension: [],
      // 时间维度
      timeDimension: [
        {
          value: 'hour',
          label: '按小时'
        },
        {
          value: 'day',
          label: '按日'
        },
        {
          value: 'week',
          label: '按周'
        },
        {
          value: 'month',
          label: '按月'
        },
        {
          value: 'term',
          label: '按学期'
        },
        {
          value: 'year',
          label: '按年'
        }
      ],
      // 时间范围
      timeRange: [
        {
          label: '今日',
          value: 1
        },
        {
          label: '昨日',
          value: 2
        },
        {
          label: '近7天',
          value: 3
        },
        {
          label: '近30天',
          value: 4
        },
        {
          label: '本月',
          value: 5
        },
        {
          label: '本学期',
          value: 6
        },
        {
          label: '本学年',
          value: 7
        },
        {
          label: '今年',
          value: 8
        },
        {
          label: '近3年',
          value: 9
        }
      ],
      // 图表类型
      chartTypeList: [
        {
          label: '表格',
          value: 'table'
        },
        {
          label: '柱状图',
          value: 'bar'
        },
        {
          label: '水平柱状图',
          value: 'horizontalBar'
        },
        {
          label: '折线图',
          value: 'line'
        },
        {
          label: '折柱混合图',
          value: 'lineBar'
        },
        {
          label: '饼图',
          value: 'pie'
        },
        {
          label: '雷达图',
          value: 'radar'
        },
        {
          label: '翻牌器',
          value: 'flop'
        },
        {
          label: '环形图',
          value: 'circleProgress'
        },
        {
          label: '仪表盘',
          value: 'gauge'
        },
        {
          label: '指标',
          value: 'statistic'
        },
        {
          label: '进度条',
          value: 'lineProgress'
        }
      ],

      allIndList: [],
      loading: false,
      // 预览相关数据
      showPreview: false,
      previewLoading: false,
      previewError: '',
      previewData: [],
      tableColumns: [],
      chartInstance: null,
      currentChartComponent: null,
      chartProps: {}
    }
  },
  computed: {
    // 判断是否可以预览
    canPreview() {
      return this.form.chartInds &&
             this.form.chartInds.length > 0 &&
             this.form.chartInds.some(item => item.indCode)
    }
  },
  created () {},
  mounted () {
    this.getAllIndList()
  },
  beforeDestroy() {
    // 清理图表组件
    this.currentChartComponent = null
    this.chartProps = {}
  },
  watch: {},
  methods: {
    // 清理预览状态
    clearPreviewState() {
      this.showPreview = false
      this.previewLoading = false
      this.previewError = ''
      this.currentChartComponent = null
      this.chartProps = {}
    },

    openDialog (row) {
      // 清理之前的预览状态
      this.clearPreviewState()
      if (row) {
        this.dialogTitle = '编辑图表模型'
        this.form = {
          ...row,
          chartType: row.chartType || '', // 设置图表类型
          chartInds: row.chartInds
            ? row.chartInds.map(item => ({
                ...item,
                chartDims:
                  item.chartDims && item.chartDims.length
                    ? {
                        ...item.chartDims[0],
                        dimValList: item.chartDims[0].dimValList
                      }
                    : {}
              }))
            : [
                {
                  indCode: '',
                  isOrNotDrillDown: 0,
                  lxbm: '',
                  chartDims: []
                }
              ],
          chartDims: row.chartDims?.length
            ? row.chartDims.map(item => ({
                ...item,
                dimValList:
                  item.dimValList && item.dimValList.length
                    ? item.dimValList
                    : []
              }))
            : [
                {
                  dimIndCode: '',
                  dimCol: '',
                  dimName: '',
                  dimValList: [],
                  dimType: 'filter'
                }
              ]
        }
        console.log(this.form, '???????????????')
        this.getSameDimension()

        row.chartInds &&
          row.chartInds.forEach((item, index) => {
            this.getDim(item.indCode, index)
          })
        // row.chartDims &&
        //   row.chartDims.forEach(item => {
        //     this.getPsOption(item.dimCol)
        //   })
      } else {
        this.dialogTitle = '新建图表模型'
        this.form = {
          chartName: '',
          chartType: '', // 初始化图表类型
          chartInds: [
            {
              indCode: '',
              isOrNotDrillDown: 0,
              indType: '',
              chartDims: ['全部']
            }
          ],
          chartDims: [
            {
              dimIndCode: '',
              dimCol: '',
              dimName: '',
              dimValList: [],
              dimType: 'filter'
            }
          ],
          description: ''
        }
      }
      this.dialogVisible = true
    },
    // 获取所有指标
    async getAllIndList () {
      const { data } =
        await this.$httpBi.indicatorAnagement.getAllIndicatorList({
          zbmc: '',
          lxbm: '',
          sysjyid: '', // 数据域id
          pxmc: '', // 排序字段名称
          px: '', // 排序方式
          pageSize: -1,
          currentPage: 1
        })
      this.allIndList = data.list
    },
    // 添加指标
    addIndicators () {
      this.form.chartInds.push({
        indCode: '',
        isOrNotDrillDown: 0,
        chartDims: []
      })
    },
    // 选择维度
    selectIdxDim (val, index) {
      console.log(val, 'val')
      if (val.fieldType === 'time') {
        this.$set(this.form.chartInds, index, {
          ...this.form.chartInds[index],
          chartDims: {
            ...val,
            dimValList: ''
          }
        })
      } else {
        this.$set(this.form.chartInds, index, {
          ...this.form.chartInds[index],
          chartDims: {
            ...val,
            dimValList: []
          }
        })
      }
    },
    addFilter () {
      this.form.chartDims.push({
        dimIndCode: '',
        dimCol: '',
        dimName: '',
        dimValList: [],
        dimType: 'filter'
      })
    },
    // 删除指标
    removeIndicators (index) {
      this.form.chartInds.splice(index, 1)
    },
    removeFilter (index) {
      this.form.chartDims.splice(index, 1)
    },
    // 获取单个指标维度
    async getDim (indCode, index, isGetSame = false) {
      this.form.chartInds[index].indType = indCode.slice(0, 2)

      const { data } = await this.$httpBi.api.paramGet(
        '/DimManage/getDimLevelByIndCode',
        {
          indCode
        }
      )
      this.$set(
        this.optionsMap,
        indCode,
        data.map(item => ({
          ...item,
          indCode,
          dimType: 'group'
        }))
      )
      this.form.chartInds[index].chartDims = {
        ...data.find(
          item =>
            item.levelCode === this.form.chartInds[index].chartDims.levelCode
        ),
        ...this.form.chartInds[index].chartDims
      }
      console.log(this.optionsMap, 'this.optionsMap')
      if (isGetSame) {
        this.form.chartInds[index].chartDims = {}

        this.getSameDimension(isGetSame)
      }
    },
    // 获取相同维度
    async getSameDimension (isGetSame) {
      let params = this.form.chartInds.map(item => ({
        indCode: item.indCode,
        lxbm: item.indType
      }))
      const { data } = await this.$httpBi.analyzeTheme.getPublicDim(params)
      this.sameDimension = data
      if (isGetSame) {
        this.form.chartDims = [
          {
            dimIndCode: '',
            dimCol: '',
            dimName: '',
            dimValList: [],
            dimType: 'filter'
          }
        ]
      }
    },
    // 选择维度项
    async changeDim (dimCol) {
      const item = {
        ...this.sameDimension.find(item => item.dimCol === dimCol)
      }
      // this.getPsOption(dimCol)
      this.$set(this.form.chartDims, 0, {
        ...item,
        indType: item.dimType,
        dimType: 'filter',
        dimValList: []
      })
    },
    async getPsOption (dimCol) {
      console.log(this.form.chartInds)
      const { data } =
        await this.$httpBi.compositeIndicator.getSelectDimensionValue({
          indType: this.form.chartInds[0].indType,
          dimCol,
          indCode: this.form.chartInds[0].indCode
        })
      console.log(data, 'data')
      this.$set(
        this.optionsMap,
        dimCol,
        data.map(item => ({
          ...item,
          dimVal: item.dimValue || ''
        }))
      )
    },
    // 预览图表
    async handlePreview() {
      if (!this.form.chartType) {
        this.$message.warning('请先选择图表类型')
        return
      }

      this.previewLoading = true
      this.previewError = ''
      this.showPreview = true

      try {
        // 使用静态数据进行预览
        const staticData = getStaticPreviewData(this.form.chartType)
        this.chartProps = getChartProps(this.form.chartType, staticData)

        // 动态加载图表组件
        await this.loadChartComponent(this.form.chartType)

      } catch (error) {
        console.error('预览失败:', error)
        this.previewError = '预览失败，请检查图表类型配置'
      } finally {
        this.previewLoading = false
      }
    },

    // 动态加载图表组件
    async loadChartComponent(chartType) {
      try {
        if (chartComponentMap[chartType]) {
          const component = await chartComponentMap[chartType]()
          this.currentChartComponent = component.default || component
        } else {
          this.previewError = `不支持的图表类型: ${chartType}`
        }
      } catch (error) {
        console.error('加载图表组件失败:', error)
        this.previewError = '加载图表组件失败'
      }
    },

    // 图表点击事件处理
    handleChartClick(params) {
      console.log('图表点击事件:', params)
    },

    // 关闭对话框
    handleClose() {
      this.clearPreviewState()
      this.dialogVisible = false
    },



    // 保存
    async handleSave () {
      console.log(this.form, 'this.form')

      this.loading = true

      this.$refs.form.validate(async valid => {
        if (valid) {
          // 检查聚类指标是否选择了维度和维度值
          const invalidClusterItems = this.form.chartInds.filter(
            item =>
              item.isOrNotDrillDown === 1 &&
              (!item.chartDims ||
                !item.chartDims.dimCol ||
                !item.chartDims.dimValList ||
                item.chartDims.dimValList.length === 0)
          )

          if (invalidClusterItems.length > 0) {
            this.$message.error('开启聚类的指标必须选择维度和维度值')
            return
          }

          let apiUrl = this.form.chartCode
            ? '/indicator/chart/update'
            : '/indicator/chart/create'
          await Request.api.paramPost(apiUrl, {
            ...this.form,
            chartType: this.form.chartType, // 确保包含图表类型
            chartInds: this.form.chartInds
              .map(item => ({
                ...item,
                chartDims:
                  item.isOrNotDrillDown === 1
                    ? [item.chartDims]
                        .filter(dim => dim !== '全部')
                        .map(dim => ({
                          ...dim,
                          dimValList:
                            dim.lxbm === 'time'
                              ? [dim.dimValList]
                              : dim.dimValList.filter(val => val !== '全部')
                        }))
                    : []
              }))
              .filter(dim => dim.indCode),
            chartDims: this.form.chartDims
              .map(dim => ({
                ...dim,
                dimValList: dim.dimValList.filter(val => val !== '全部')
              }))
              .filter(dim => dim.dimCol)
          })
          this.loading = false
          this.$message.success('保存成功')
          this.$emit('refresh')
          this.clearPreviewState()
          this.dialogVisible = false
        }
      })
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .el-dialog__header {
  margin: 0 24px;
  padding: 20px 0;
  padding-bottom: 10px;
  border-bottom: 1px solid #edeff0;
  font-size: 16px;
  font-family: PingFangSC, PingFang SC;
  font-weight: 500;
  color: #222222;
}

::v-deep .el-dialog__body {
  padding: 20px 24px;
  color: #606266;
  font-size: 14px;
  word-break: break-all;
}

.chart-preview-container {
  margin-top: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background: #fff;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #e4e7ed;
    background: #f5f7fa;

    h4 {
      margin: 0;
      font-size: 14px;
      font-weight: 500;
      color: #303133;
    }
  }

  .preview-content {
    padding: 16px;
    min-height: 200px;

    .preview-error {
      text-align: center;
      color: #f56c6c;
      padding: 40px 0;
    }

    .preview-empty {
      text-align: center;
      color: #909399;
      padding: 40px 0;
    }

    .chart-container {
      width: 100%;
    }
  }
}
</style>

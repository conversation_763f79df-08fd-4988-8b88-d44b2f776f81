// 图表组件映射配置
export const chartComponentMap = {
  table: () => import("@/components/CommonTable.vue"),
  bar: () => import("@/components/Charts/ChartColumn.vue"),
  horizontalBar: () => import("@/components/Charts/ChartBar.vue"),
  line: () => import("@/components/Charts/ChartLine.vue"),
  lineBar: () => import("@/components/Charts/ChartDoubleAxis.vue"),
  pie: () => import("@/components/Charts/ChartPie.vue"),
  radar: () => import("@/components/Charts/ChartPie.vue"), // 暂时用饼图代替
  flop: () => import("@/components/Charts/ChartPictorialBar.vue"),
  circleProgress: () => import("@/components/Charts/ChartPie.vue"), // 暂时用饼图代替
  gauge: () => import("@/components/Charts/ChartPie.vue"), // 暂时用饼图代替
  statistic: () => import("@/components/Charts/ChartColumn.vue"), // 暂时用柱状图代替
  lineProgress: () => import("@/components/Charts/ChartBar.vue") // 暂时用条形图代替
}

// 静态预览数据
export const getStaticPreviewData = chartType => {
  const baseData = [
    { name: "语文", value: 85, category: "文科", score: 85 },
    { name: "数学", value: 92, category: "理科", score: 92 },
    { name: "英语", value: 78, category: "文科", score: 78 },
    { name: "物理", value: 88, category: "理科", score: 88 },
    { name: "化学", value: 90, category: "理科", score: 90 },
    { name: "历史", value: 82, category: "文科", score: 82 }
  ]

  const timeSeriesData = [
    { date: "2024-01", value: 120, count: 45 },
    { date: "2024-02", value: 132, count: 52 },
    { date: "2024-03", value: 101, count: 38 },
    { date: "2024-04", value: 134, count: 48 },
    { date: "2024-05", value: 90, count: 35 },
    { date: "2024-06", value: 230, count: 68 }
  ]

  const statisticData = [
    { label: "总数", value: 1234, unit: "个" },
    { label: "增长率", value: 15.6, unit: "%" },
    { label: "完成率", value: 89.2, unit: "%" }
  ]

  switch (chartType) {
    case "table":
      return {
        data: baseData,
        columns: [
          { prop: "name", label: "科目" },
          { prop: "value", label: "分数" },
          { prop: "category", label: "类别" }
        ]
      }

    case "bar":
    case "horizontalBar":
      return {
        data: baseData,
        xField: "name",
        yField: "value",
        seriesName: "分数"
      }

    case "line":
      return {
        data: timeSeriesData,
        xField: "date",
        yField: "value",
        seriesName: "数值趋势"
      }

    case "lineBar":
      return {
        data: timeSeriesData,
        xField: "date",
        yField: ["value", "count"],
        seriesName: ["数值", "数量"]
      }

    case "pie":
      return {
        data: baseData,
        colorField: "name",
        angleField: "value",
        seriesName: "分数分布"
      }

    case "radar":
      return {
        data: baseData.map(item => ({
          ...item,
          max: 100
        })),
        angleField: "name",
        radiusField: "value"
      }

    case "flop":
    case "statistic":
      return {
        data: statisticData[0],
        value: statisticData[0].value,
        label: statisticData[0].label,
        unit: statisticData[0].unit
      }

    case "circleProgress":
    case "gauge":
      return {
        data: { value: 75, max: 100 },
        value: 75,
        max: 100,
        unit: "%"
      }

    case "lineProgress":
      return {
        data: statisticData,
        items: statisticData
      }

    default:
      return {
        data: baseData,
        xField: "name",
        yField: "value"
      }
  }
}

// 图表组件属性配置
export const getChartProps = (chartType, data) => {
  const commonProps = {
    width: "100%",
    height: "300px"
  }

  switch (chartType) {
    case "table":
      return {
        ...commonProps,
        tableData: data.data,
        tableColumns: data.columns,
        showSelection: false,
        showBatchTag: false,
        loading: false
      }

    case "bar":
    case "horizontalBar":
      return {
        ...commonProps,
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName
      }

    case "line":
      return {
        ...commonProps,
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName
      }

    case "lineBar":
      return {
        ...commonProps,
        chartData: data.data,
        xField: data.xField,
        yField: data.yField,
        seriesName: data.seriesName
      }

    case "pie":
      return {
        ...commonProps,
        chartData: data.data,
        colorField: data.colorField,
        angleField: data.angleField,
        seriesName: data.seriesName
      }

    case "radar":
      return {
        ...commonProps,
        chartData: data.data,
        angleField: data.angleField,
        radiusField: data.radiusField
      }

    case "flop":
    case "statistic":
      return {
        ...commonProps,
        value: data.value,
        label: data.label,
        unit: data.unit
      }

    case "circleProgress":
    case "gauge":
      return {
        ...commonProps,
        value: data.value,
        max: data.max,
        unit: data.unit
      }

    case "lineProgress":
      return {
        ...commonProps,
        items: data.items
      }

    default:
      return {
        ...commonProps,
        chartData: data.data,
        xField: data.xField,
        yField: data.yField
      }
  }
}
